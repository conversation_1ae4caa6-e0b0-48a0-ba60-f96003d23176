{"name": "sacraltrack", "version": "0.2.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "clean": "rm -rf .next && next dev"}, "dependencies": {"@ffmpeg/core": "^0.10.0", "@ffmpeg/ffmpeg": "^0.10.1", "@ffmpeg/util": "^0.12.2", "@headlessui/react": "^2.2.0", "@heroicons/react": "^2.2.0", "@nextui-org/react": "^2.2.10", "@stripe/react-stripe-js": "^2.6.2", "@stripe/stripe-js": "^3.5.0", "@tanstack/react-virtual": "^3.13.9", "@types/formidable": "^3.4.5", "@types/hls.js": "^0.13.3", "@vercel/blob": "^1.0.0", "autoprefixer": "10.4.15", "axios": "^1.6.8", "bcryptjs": "^3.0.2", "browser-image-compression": "^2.0.2", "canvas-confetti": "^1.9.3", "critters": "^0.0.23", "date-fns": "^4.1.0", "ethers": "^6.13.5", "ffmpeg": "^0.0.4", "firebase": "^11.5.0", "fluent-ffmpeg": "^2.1.3", "form-data": "^4.0.2", "formidable": "^3.5.2", "framer-motion": "^12.6.2", "hls.js": "^1.5.20", "image-js": "^0.35.5", "jsonwebtoken": "^9.0.2", "jszip": "^3.10.1", "moment": "^2.29.4", "next": "^15.2.4", "next-auth": "^4.24.7", "node-appwrite": "^15.0.1", "node-fetch": "^2.7.0", "node-id3": "^0.2.8", "openai": "^4.87.3", "otplib": "^12.0.1", "parse-multipart-data": "^1.5.0", "postcss": "^8.4.38", "react": "^19.1.0", "react-advanced-cropper": "^0.20.0", "react-dom": "^19.1.0", "react-error-boundary": "^5.0.0", "react-hot-toast": "^2.5.2", "react-icons": "^4.10.1", "react-infinite-scroll-hook": "^6.0.0", "react-intersection-observer": "^9.16.0", "react-responsive": "^10.0.0", "react-swipeable": "^7.0.2", "react-tooltip": "^5.28.0", "react-webcam": "^7.2.0", "react-yandex-metrika": "^2.6.0", "sharp": "^0.33.5", "stripe": "^14.25.0", "styled-jsx": "^5.1.6", "suspense": "^0.0.53", "tailwindcss": "3.3.3", "tailwindcss-scrollbar": "^0.1.0", "twilio": "^5.5.0", "typescript": "5.1.6", "uuid": "^11.1.0", "wasm-feature-detect": "^1.8.0", "wavesurfer.js": "^7.7.5", "zustand": "^4.5.2"}, "devDependencies": {"@netlify/plugin-nextjs": "^5.10.7", "@types/canvas-confetti": "^1.9.0", "@types/fluent-ffmpeg": "^2.1.27", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^22.5.4", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "appwrite": "^13.0.1", "hardhat": "^2.22.17", "postcss-import": "^16.1.0", "postcss-nesting": "^13.0.1", "postcss-preset-env": "^10.2.0"}, "optionalDependencies": {"canvas": "^2.11.2"}}