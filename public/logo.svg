<svg width="120" height="120" viewBox="0 0 120 120" xmlns="http://www.w3.org/2000/svg">
  <!-- Градиентный фон -->
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#2E2469" />
      <stop offset="100%" stop-color="#351E43" />
    </linearGradient>
    <linearGradient id="textGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#20DDBB" />
      <stop offset="100%" stop-color="#5D59FF" />
    </linearGradient>
    <filter id="glow" x="-30%" y="-30%" width="160%" height="160%">
      <feGaussianBlur stdDeviation="3" result="blur" />
      <feComposite in="SourceGraphic" in2="blur" operator="over" />
    </filter>
  </defs>
  
  <!-- Фоновый круг с градиентом -->
  <circle cx="60" cy="60" r="55" fill="url(#bgGradient)" />
  
  <!-- Внешний круг с обводкой -->
  <circle cx="60" cy="60" r="55" fill="none" stroke="url(#textGradient)" stroke-width="2" opacity="0.7" />
  
  <!-- Буква S с эффектом свечения -->
  <g filter="url(#glow)">
    <path d="M38 40 C 38 35, 45 30, 55 30 C 70 30, 75 40, 75 45 C 75 55, 65 60, 55 60 C 45 60, 40 65, 40 70 C 40 80, 50 85, 65 85 C 72 85, 78 82, 82 80" 
          stroke="url(#textGradient)" stroke-width="10" stroke-linecap="round" fill="none" />
  </g>
  
  <!-- Буква T с эффектом свечения -->
  <g filter="url(#glow)">
    <path d="M60 35 L 60 85" stroke="url(#textGradient)" stroke-width="10" stroke-linecap="round" fill="none" />
    <path d="M40 40 L 80 40" stroke="url(#textGradient)" stroke-width="10" stroke-linecap="round" fill="none" />
  </g>
</svg> 