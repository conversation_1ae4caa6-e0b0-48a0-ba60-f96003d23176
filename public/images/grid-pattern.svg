<svg width="100" height="100" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse">
      <path d="M 10 0 L 0 0 0 10" fill="none" stroke="white" stroke-width="0.5" stroke-opacity="0.2"/>
    </pattern>
    <radialGradient id="glow" cx="50%" cy="50%" r="50%" fx="50%" fy="50%">
      <stop offset="0%" stop-color="white" stop-opacity="0.2" />
      <stop offset="100%" stop-color="white" stop-opacity="0" />
    </radialGradient>
  </defs>
  <rect width="100" height="100" fill="url(#grid)" />
  <circle cx="50" cy="50" r="30" fill="url(#glow)" />
</svg> 