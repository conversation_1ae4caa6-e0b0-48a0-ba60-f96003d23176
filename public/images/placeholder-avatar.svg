<?xml version="1.0" encoding="UTF-8"?>
<svg width="200" height="200" viewBox="0 0 200 200" fill="none" xmlns="http://www.w3.org/2000/svg">
    <defs>
        <linearGradient id="paint0_linear" x1="0" y1="0" x2="200" y2="200" gradientUnits="userSpaceOnUse">
            <stop stop-color="#9333EA"/>
            <stop offset="1" stop-color="#4F46E5"/>
        </linearGradient>
    </defs>
    
    <!-- Background circle -->
    <circle cx="100" cy="100" r="100" fill="url(#paint0_linear)"/>
    
    <!-- User silhouette -->
    <path fill-rule="evenodd" clip-rule="evenodd" d="M100 120C116.569 120 130 106.569 130 90C130 73.4315 116.569 60 100 60C83.4315 60 70 73.4315 70 90C70 106.569 83.4315 120 100 120ZM100 140C66.8629 140 40 153.333 40 170V200H160V170C160 153.333 133.137 140 100 140Z" fill="#1E2136"/>
</svg> 