<svg xmlns="http://www.w3.org/2000/svg" width="400" height="400" viewBox="0 0 400 400">
  <!-- Definitions for gradients and animations -->
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#1A2338">
        <animate attributeName="stop-color" values="#1A2338; #24183D; #1A2338" dur="8s" repeatCount="indefinite" />
      </stop>
      <stop offset="100%" stop-color="#24183D">
        <animate attributeName="stop-color" values="#24183D; #1A2338; #24183D" dur="8s" repeatCount="indefinite" />
      </stop>
    </linearGradient>
    
    <radialGradient id="vinylGradient" cx="50%" cy="50%" r="50%" fx="50%" fy="50%">
      <stop offset="0%" stop-color="#000" stop-opacity="1" />
      <stop offset="70%" stop-color="#000" stop-opacity="1" />
      <stop offset="100%" stop-color="#111" stop-opacity="1" />
    </radialGradient>
    
    <filter id="glow" x="-20%" y="-20%" width="140%" height="140%">
      <feGaussianBlur stdDeviation="4" result="blur" />
      <feComposite in="SourceGraphic" in2="blur" operator="over" />
    </filter>
    
    <!-- Spinning animation for vinyl record -->
    <animateTransform id="spin" 
      attributeName="transform"
      attributeType="XML"
      type="rotate"
      from="0 200 200"
      to="360 200 200"
      dur="20s"
      repeatCount="indefinite"
    />
  </defs>
  
  <!-- Background with animated gradient -->
  <rect width="400" height="400" fill="url(#bgGradient)" rx="20" ry="20" />
  
  <!-- Decorative sound wave circles -->
  <g opacity="0.15">
    <circle cx="200" cy="200" r="190" fill="none" stroke="#fff" stroke-width="1">
      <animate attributeName="r" values="190;195;190" dur="3s" repeatCount="indefinite" />
    </circle>
    <circle cx="200" cy="200" r="180" fill="none" stroke="#fff" stroke-width="1">
      <animate attributeName="r" values="180;175;180" dur="4s" repeatCount="indefinite" />
    </circle>
    <circle cx="200" cy="200" r="170" fill="none" stroke="#fff" stroke-width="1">
      <animate attributeName="r" values="170;175;170" dur="5s" repeatCount="indefinite" />
    </circle>
    <circle cx="200" cy="200" r="160" fill="none" stroke="#fff" stroke-width="1">
      <animate attributeName="r" values="160;155;160" dur="6s" repeatCount="indefinite" />
    </circle>
  </g>
  
  <!-- Vinyl record -->
  <g>
    <!-- Main vinyl disc -->
    <circle cx="200" cy="200" r="120" fill="url(#vinylGradient)" />
    
    <!-- Vinyl grooves -->
    <g stroke="#333" fill="none" opacity="0.4">
      <circle cx="200" cy="200" r="115" />
      <circle cx="200" cy="200" r="105" />
      <circle cx="200" cy="200" r="95" />
      <circle cx="200" cy="200" r="85" />
      <circle cx="200" cy="200" r="75" />
      <circle cx="200" cy="200" r="65" />
      <circle cx="200" cy="200" r="55" />
      <circle cx="200" cy="200" r="45" />
    </g>
    
    <!-- Vinyl label -->
    <circle cx="200" cy="200" r="40" fill="#20DDBB">
      <animate attributeName="fill" values="#20DDBB;#5D59FF;#20DDBB" dur="10s" repeatCount="indefinite" />
    </circle>
    <circle cx="200" cy="200" r="35" fill="#111" opacity="0.1" />
    
    <!-- Vinyl center hole -->
    <circle cx="200" cy="200" r="7" fill="#111" />
    
    <!-- Reflection highlight -->
    <ellipse cx="160" cy="160" rx="25" ry="10" fill="#fff" opacity="0.1" transform="rotate(-30, 160, 160)" />
    
    <!-- Apply spinning animation to the entire vinyl -->
    <animateTransform attributeName="transform" attributeType="XML" type="rotate" from="0 200 200" to="360 200 200" dur="20s" repeatCount="indefinite" />
  </g>
  
  <!-- Animated sound waves -->
  <g filter="url(#glow)">
    <!-- Left wave -->
    <path d="M 50,200 Q 75,150 100,200 Q 125,250 150,200" fill="none" stroke="#20DDBB" stroke-width="3" stroke-linecap="round">
      <animate attributeName="d" values="M 50,200 Q 75,150 100,200 Q 125,250 150,200;M 50,200 Q 75,250 100,200 Q 125,150 150,200;M 50,200 Q 75,150 100,200 Q 125,250 150,200" dur="1.5s" repeatCount="indefinite" />
    </path>
    
    <!-- Right wave -->
    <path d="M 250,200 Q 275,150 300,200 Q 325,250 350,200" fill="none" stroke="#20DDBB" stroke-width="3" stroke-linecap="round">
      <animate attributeName="d" values="M 250,200 Q 275,150 300,200 Q 325,250 350,200;M 250,200 Q 275,250 300,200 Q 325,150 350,200;M 250,200 Q 275,150 300,200 Q 325,250 350,200" dur="1.5s" repeatCount="indefinite" />
    </path>
  </g>
  
  <!-- Floating particles -->
  <g>
    <circle cx="0" cy="0" r="3" fill="#20DDBB" opacity="0.7">
      <animateMotion dur="6s" repeatCount="indefinite" path="M50,50 C80,30 120,60 150,40" />
    </circle>
    
    <circle cx="0" cy="0" r="2" fill="#5D59FF" opacity="0.5">
      <animateMotion dur="8s" repeatCount="indefinite" path="M320,80 C350,60 380,100 410,80" />
    </circle>
    
    <circle cx="0" cy="0" r="2.5" fill="#20DDBB" opacity="0.6">
      <animateMotion dur="7s" repeatCount="indefinite" path="M60,280 C90,260 120,300 150,280" />
    </circle>
    
    <circle cx="0" cy="0" r="1.5" fill="#fff" opacity="0.7">
      <animateMotion dur="5s" repeatCount="indefinite" path="M280,320 C310,300 340,340 370,320" />
    </circle>
  </g>
  
  <!-- Subtle grid pattern -->
  <g opacity="0.1" stroke="#fff" stroke-width="0.5">
    <line x1="0" y1="100" x2="400" y2="100" />
    <line x1="0" y1="200" x2="400" y2="200" />
    <line x1="0" y1="300" x2="400" y2="300" />
    <line x1="100" y1="0" x2="100" y2="400" />
    <line x1="200" y1="0" x2="200" y2="400" />
    <line x1="300" y1="0" x2="300" y2="400" />
  </g>
</svg> 