<svg xmlns="http://www.w3.org/2000/svg" width="400" height="400" viewBox="0 0 400 400">
  <!-- Definitions for gradients and animations -->
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#1E2136">
        <animate attributeName="stop-color" values="#1E2136; #24183D; #1E2136" dur="10s" repeatCount="indefinite" />
      </stop>
      <stop offset="100%" stop-color="#24183D">
        <animate attributeName="stop-color" values="#24183D; #1E2136; #24183D" dur="10s" repeatCount="indefinite" />
      </stop>
    </linearGradient>
    
    <linearGradient id="mountainGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" stop-color="#20DDBB" stop-opacity="0.3" />
      <stop offset="100%" stop-color="#20DDBB" stop-opacity="0.1" />
    </linearGradient>
    
    <linearGradient id="skyGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" stop-color="#5D59FF" stop-opacity="0.1" />
      <stop offset="100%" stop-color="#20DDBB" stop-opacity="0.05" />
    </linearGradient>
    
    <filter id="glow" x="-20%" y="-20%" width="140%" height="140%">
      <feGaussianBlur stdDeviation="4" result="blur" />
      <feComposite in="SourceGraphic" in2="blur" operator="over" />
    </filter>
    
    <!-- Star twinkle animation -->
    <radialGradient id="starGlow" cx="50%" cy="50%" r="50%">
      <stop offset="0%" stop-color="#fff" stop-opacity="1" />
      <stop offset="100%" stop-color="#fff" stop-opacity="0" />
    </radialGradient>
  </defs>
  
  <!-- Background with animated gradient -->
  <rect width="400" height="400" fill="url(#bgGradient)" rx="20" ry="20" />
  
  <!-- Sky gradient -->
  <rect width="400" height="250" fill="url(#skyGradient)" rx="20" ry="20" />
  
  <!-- Stars with twinkling effect -->
  <g>
    <circle cx="50" cy="50" r="1.5" fill="#fff" opacity="0.8">
      <animate attributeName="opacity" values="0.8;0.2;0.8" dur="3s" repeatCount="indefinite" />
    </circle>
    <circle cx="120" cy="30" r="1" fill="#fff" opacity="0.7">
      <animate attributeName="opacity" values="0.7;0.3;0.7" dur="5s" repeatCount="indefinite" />
    </circle>
    <circle cx="200" cy="70" r="1.8" fill="#fff" opacity="0.9">
      <animate attributeName="opacity" values="0.9;0.4;0.9" dur="4s" repeatCount="indefinite" />
    </circle>
    <circle cx="280" cy="40" r="1.2" fill="#fff" opacity="0.6">
      <animate attributeName="opacity" values="0.6;0.1;0.6" dur="6s" repeatCount="indefinite" />
    </circle>
    <circle cx="350" cy="60" r="1.5" fill="#fff" opacity="0.8">
      <animate attributeName="opacity" values="0.8;0.3;0.8" dur="3.5s" repeatCount="indefinite" />
    </circle>
    <circle cx="80" cy="100" r="1" fill="#fff" opacity="0.7">
      <animate attributeName="opacity" values="0.7;0.2;0.7" dur="4.5s" repeatCount="indefinite" />
    </circle>
    <circle cx="170" cy="120" r="1.3" fill="#fff" opacity="0.8">
      <animate attributeName="opacity" values="0.8;0.3;0.8" dur="5s" repeatCount="indefinite" />
    </circle>
    <circle cx="250" cy="90" r="1.1" fill="#fff" opacity="0.6">
      <animate attributeName="opacity" values="0.6;0.1;0.6" dur="3s" repeatCount="indefinite" />
    </circle>
    <circle cx="320" cy="110" r="1.4" fill="#fff" opacity="0.9">
      <animate attributeName="opacity" values="0.9;0.4;0.9" dur="4s" repeatCount="indefinite" />
    </circle>
  </g>
  
  <!-- Moon with soft glow -->
  <g filter="url(#glow)">
    <circle cx="320" cy="80" r="15" fill="#fff" opacity="0.9" />
    <circle cx="310" cy="75" r="5" fill="#1E2136" opacity="0.1" />
  </g>
  
  <!-- Mountain range silhouette -->
  <g>
    <!-- Back mountains -->
    <path d="M0,230 L40,180 L80,210 L120,160 L160,190 L200,140 L240,180 L280,150 L320,190 L360,160 L400,200 L400,400 L0,400 Z" 
          fill="url(#mountainGradient)" opacity="0.6" />
    
    <!-- Front mountains with animated glow -->
    <path d="M0,250 L50,200 L100,240 L150,180 L200,220 L250,170 L300,230 L350,180 L400,240 L400,400 L0,400 Z" 
          fill="#20DDBB" opacity="0.2" filter="url(#glow)">
      <animate attributeName="opacity" values="0.2;0.3;0.2" dur="8s" repeatCount="indefinite" />
    </path>
  </g>
  
  <!-- Abstract frame/border -->
  <rect x="10" y="10" width="380" height="380" rx="15" ry="15" 
        fill="none" stroke="#20DDBB" stroke-width="2" stroke-opacity="0.3" 
        stroke-dasharray="5,5" />
  
  <!-- Decorative corner elements -->
  <g stroke="#20DDBB" stroke-width="2" stroke-opacity="0.6">
    <path d="M20,20 L50,20 M20,20 L20,50" />
    <path d="M380,20 L350,20 M380,20 L380,50" />
    <path d="M20,380 L50,380 M20,380 L20,350" />
    <path d="M380,380 L350,380 M380,380 L380,350" />
  </g>
  
  <!-- Floating particles -->
  <g>
    <circle cx="0" cy="0" r="3" fill="#20DDBB" opacity="0.6">
      <animateMotion dur="15s" repeatCount="indefinite" path="M100,300 C150,280 200,320 250,300" />
    </circle>
    
    <circle cx="0" cy="0" r="2" fill="#5D59FF" opacity="0.5">
      <animateMotion dur="12s" repeatCount="indefinite" path="M200,320 C250,300 300,340 350,320" />
    </circle>
    
    <circle cx="0" cy="0" r="1.5" fill="#fff" opacity="0.7">
      <animateMotion dur="10s" repeatCount="indefinite" path="M50,280 C100,260 150,300 200,280" />
    </circle>
  </g>
  
  <!-- Center image placeholder icon -->
  <g transform="translate(175, 175)" opacity="0.6">
    <rect x="-25" y="-25" width="50" height="50" rx="5" ry="5" fill="none" stroke="#fff" stroke-width="2" />
    <path d="M-15,-5 L-5,-15 L10,0 L-5,15 L-15,5 Z" fill="#fff" />
    <circle cx="10" cy="-10" r="5" fill="#fff" />
    <path d="M-15,15 L15,-15 M-15,-15 L15,15" stroke="#fff" stroke-width="1" opacity="0.5" />
  </g>
  
  <!-- Subtle grid overlay -->
  <g stroke="#fff" stroke-width="0.2" opacity="0.05">
    <line x1="0" y1="100" x2="400" y2="100" />
    <line x1="0" y1="200" x2="400" y2="200" />
    <line x1="0" y1="300" x2="400" y2="300" />
    <line x1="100" y1="0" x2="100" y2="400" />
    <line x1="200" y1="0" x2="200" y2="400" />
    <line x1="300" y1="0" x2="300" y2="400" />
  </g>
</svg> 