<svg xmlns="http://www.w3.org/2000/svg" width="400" height="400" viewBox="0 0 400 400">
  <!-- Definitions for gradients without animations -->
  <defs>
    <!-- Основной градиент фона -->
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#1E2136" />
      <stop offset="100%" stop-color="#141625" />
    </linearGradient>
    
    <!-- Градиент для виниловой пластинки -->
    <radialGradient id="vinylGradient" cx="50%" cy="50%" r="50%" fx="50%" fy="50%">
      <stop offset="0%" stop-color="#2A2D47" />
      <stop offset="30%" stop-color="#1E2136" />
      <stop offset="70%" stop-color="#141625" />
      <stop offset="100%" stop-color="#0e101a" />
    </radialGradient>
    
    <!-- Градиент для пульсирующего круга -->
    <radialGradient id="pulseGradient" cx="50%" cy="50%" r="50%" fx="50%" fy="50%">
      <stop offset="0%" stop-color="#20DDBB" stop-opacity="0.6" />
      <stop offset="70%" stop-color="#5D59FF" stop-opacity="0.3" />
      <stop offset="100%" stop-color="#5D59FF" stop-opacity="0" />
    </radialGradient>
    
    <!-- Эффекты свечения -->
    <filter id="glow" x="-20%" y="-20%" width="140%" height="140%">
      <feGaussianBlur stdDeviation="5" result="blur" />
      <feComposite in="SourceGraphic" in2="blur" operator="over" />
    </filter>
    
    <!-- Клипмаск для круглой аватарки -->
    <clipPath id="circleMask">
      <circle cx="200" cy="200" r="140" />
    </clipPath>
    
    <!-- Градиент для свечения от элементов -->
    <linearGradient id="glowGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#20DDBB" />
      <stop offset="100%" stop-color="#5D59FF" />
    </linearGradient>
  </defs>
  
  <!-- Фон с статичным градиентом -->
  <rect width="400" height="400" fill="url(#bgGradient)" rx="30" ry="30" />
  
  <!-- Фоновая сетка -->
  <g opacity="0.1">
    <path d="M0,40 L400,40 M0,80 L400,80 M0,120 L400,120 M0,160 L400,160 M0,200 L400,200 M0,240 L400,240 M0,280 L400,280 M0,320 L400,320 M0,360 L400,360" 
          stroke="#5D59FF" stroke-width="0.5" />
    <path d="M40,0 L40,400 M80,0 L80,400 M120,0 L120,400 M160,0 L160,400 M200,0 L200,400 M240,0 L240,400 M280,0 L280,400 M320,0 L320,400 M360,0 L360,400" 
          stroke="#5D59FF" stroke-width="0.5" />
  </g>
  
  <!-- Статичный круг -->
  <circle cx="200" cy="200" r="150" fill="url(#pulseGradient)" opacity="0.4" />
  
  <!-- Декоративные круги и линии без анимации -->
  <g opacity="0.6">
    <circle cx="200" cy="200" r="165" stroke="#20DDBB" stroke-width="0.5" fill="none" stroke-dasharray="1,5" />
    <circle cx="200" cy="200" r="155" stroke="#5D59FF" stroke-width="0.5" fill="none" stroke-dasharray="1,3" />
  </g>
  
  <!-- Виниловая пластинка как основа аватара -->
  <g clip-path="url(#circleMask)">
    <!-- Основной круг пластинки -->
    <circle cx="200" cy="200" r="140" fill="url(#vinylGradient)" />
    
    <!-- Концентрические круги на пластинке -->
    <circle cx="200" cy="200" r="120" stroke="#20DDBB" stroke-width="0.5" fill="none" opacity="0.3" />
    <circle cx="200" cy="200" r="100" stroke="#5D59FF" stroke-width="0.5" fill="none" opacity="0.3" />
    <circle cx="200" cy="200" r="80" stroke="#20DDBB" stroke-width="0.5" fill="none" opacity="0.3" />
    <circle cx="200" cy="200" r="60" stroke="#5D59FF" stroke-width="0.5" fill="none" opacity="0.3" />
    <circle cx="200" cy="200" r="40" stroke="#20DDBB" stroke-width="0.5" fill="none" opacity="0.3" />
    
    <!-- Центральное отверстие пластинки -->
    <circle cx="200" cy="200" r="20" fill="#0e101a" stroke="#20DDBB" stroke-width="1" />
    <circle cx="200" cy="200" r="6" fill="#5D59FF" />
  </g>
  
  <!-- Музыкальные элементы вокруг аватара без анимации -->
  <g filter="url(#glow)">
    <!-- Музыкальная нота 1 -->
    <g transform="translate(100, 70)" fill="url(#glowGradient)" opacity="0.6">
      <circle cx="0" cy="0" r="6" />
      <path d="M0,0 L0,40 C0,50 20,50 20,40 L20,30" fill="none" stroke="url(#glowGradient)" stroke-width="3" stroke-linecap="round" />
      <circle cx="20" cy="30" r="8" />
    </g>
    
    <!-- Музыкальная нота 2 -->
    <g transform="translate(300, 100)" fill="url(#glowGradient)" opacity="0.6">
      <circle cx="0" cy="0" r="5" />
      <path d="M0,0 L0,35 C0,45 15,45 15,35 L15,25" fill="none" stroke="url(#glowGradient)" stroke-width="2.5" stroke-linecap="round" />
      <circle cx="15" cy="25" r="7" />
    </g>
    
    <!-- Музыкальная нота 3 -->
    <g transform="translate(80, 300)" fill="url(#glowGradient)" opacity="0.6">
      <circle cx="0" cy="0" r="4" />
      <path d="M0,0 L0,30 C0,40 12,40 12,30 L12,20" fill="none" stroke="url(#glowGradient)" stroke-width="2" stroke-linecap="round" />
      <circle cx="12" cy="20" r="6" />
    </g>
    
    <!-- Музыкальная нота 4 -->
    <g transform="translate(320, 280)" fill="url(#glowGradient)" opacity="0.6">
      <circle cx="0" cy="0" r="5" />
      <path d="M0,0 L0,32 C0,42 14,42 14,32 L14,22" fill="none" stroke="url(#glowGradient)" stroke-width="2.5" stroke-linecap="round" />
      <circle cx="14" cy="22" r="7" />
    </g>
  </g>
  
  <!-- Звуковые волны без анимации -->
  <g opacity="0.4">
    <path d="M50,200 Q75,180 100,200 Q125,220 150,200" stroke="#20DDBB" stroke-width="2" fill="none" />
    <path d="M250,200 Q275,180 300,200 Q325,220 350,200" stroke="#5D59FF" stroke-width="2" fill="none" />
    <path d="M200,50 Q220,75 200,100 Q180,125 200,150" stroke="#20DDBB" stroke-width="2" fill="none" />
    <path d="M200,250 Q220,275 200,300 Q180,325 200,350" stroke="#5D59FF" stroke-width="2" fill="none" />
  </g>
  
  <!-- Статичная установка для DJ -->
  <g transform="translate(200, 340)">
    <!-- Основание DJ-пульта -->
    <rect x="-45" y="-15" width="90" height="30" rx="5" ry="5" fill="#20DDBB" opacity="0.2" />
    
    <!-- Кнопки на пульте -->
    <circle cx="-25" cy="0" r="5" fill="#20DDBB" opacity="0.7" />
    <circle cx="0" cy="0" r="5" fill="#5D59FF" opacity="0.7" />
    <circle cx="25" cy="0" r="5" fill="#20DDBB" opacity="0.7" />
    
    <!-- Статичный ползунок -->
    <rect x="-40" y="10" width="80" height="3" rx="1.5" ry="1.5" fill="#5D59FF" opacity="0.5" />
    <rect x="-5" y="8" width="10" height="7" rx="2" ry="2" fill="#20DDBB" opacity="0.8" />
  </g>
  
  <!-- Имитация текста Music Artist -->
  <g transform="translate(200, 85)">
    <path d="M-70,0 L-40,0 M-30,0 L-20,0 M-10,0 L10,0 M20,0 L40,0 M50,0 L70,0" stroke="#5D59FF" stroke-width="2" opacity="0.7" />
    <path d="M-50,-15 L-40,-15 M-30,-15 L-10,-15 M0,-15 L20,-15 M30,-15 L50,-15" stroke="#20DDBB" stroke-width="2" opacity="0.7" />
  </g>
</svg>
