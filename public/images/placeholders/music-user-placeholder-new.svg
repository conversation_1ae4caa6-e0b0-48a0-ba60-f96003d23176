<svg width="400" height="400" viewBox="0 0 400 400" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background gradient -->
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1a1a2e;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#16213e;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#0f3460;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="userGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#20ddbb;stop-opacity:0.8" />
      <stop offset="100%" style="stop-color:#5d59ff;stop-opacity:0.8" />
    </linearGradient>
    
    <linearGradient id="noteGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ff6b6b;stop-opacity:0.6" />
      <stop offset="50%" style="stop-color:#4ecdc4;stop-opacity:0.6" />
      <stop offset="100%" style="stop-color:#45b7d1;stop-opacity:0.6" />
    </linearGradient>
    
    <filter id="glow">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- Background -->
  <rect width="400" height="400" fill="url(#bgGradient)"/>
  
  <!-- Decorative circles -->
  <circle cx="80" cy="80" r="30" fill="#20ddbb" opacity="0.1"/>
  <circle cx="320" cy="120" r="25" fill="#5d59ff" opacity="0.1"/>
  <circle cx="350" cy="300" r="35" fill="#ff6b6b" opacity="0.1"/>
  <circle cx="60" cy="320" r="20" fill="#4ecdc4" opacity="0.1"/>
  
  <!-- Main user silhouette -->
  <g transform="translate(200, 200)">
    <!-- Head -->
    <circle cx="0" cy="-40" r="45" fill="url(#userGradient)" opacity="0.7"/>
    
    <!-- Body -->
    <ellipse cx="0" cy="20" rx="55" ry="70" fill="url(#userGradient)" opacity="0.7"/>
    
    <!-- Headphones -->
    <path d="M -35 -55 Q -50 -65 -50 -40 Q -50 -15 -35 -25" stroke="#20ddbb" stroke-width="4" fill="none" opacity="0.8"/>
    <path d="M 35 -55 Q 50 -65 50 -40 Q 50 -15 35 -25" stroke="#20ddbb" stroke-width="4" fill="none" opacity="0.8"/>
    <path d="M -35 -55 Q 0 -70 35 -55" stroke="#20ddbb" stroke-width="3" fill="none" opacity="0.8"/>
    
    <!-- Headphone pads -->
    <ellipse cx="-42" cy="-40" rx="8" ry="12" fill="#20ddbb" opacity="0.6"/>
    <ellipse cx="42" cy="-40" rx="8" ry="12" fill="#20ddbb" opacity="0.6"/>
  </g>
  
  <!-- Musical notes -->
  <g fill="url(#noteGradient)" filter="url(#glow)">
    <!-- Note 1 -->
    <g transform="translate(120, 150)">
      <ellipse cx="0" cy="8" rx="6" ry="4" transform="rotate(-20)"/>
      <rect x="5" y="-15" width="2" height="25"/>
      <path d="M 7 -15 Q 15 -18 15 -10 L 15 5" stroke="currentColor" stroke-width="2" fill="none"/>
    </g>
    
    <!-- Note 2 -->
    <g transform="translate(290, 180)">
      <ellipse cx="0" cy="8" rx="6" ry="4" transform="rotate(15)"/>
      <rect x="5" y="-15" width="2" height="25"/>
    </g>
    
    <!-- Note 3 -->
    <g transform="translate(150, 280)">
      <ellipse cx="0" cy="8" rx="5" ry="3" transform="rotate(-10)"/>
      <rect x="4" y="-12" width="1.5" height="20"/>
    </g>
    
    <!-- Note 4 -->
    <g transform="translate(280, 120)">
      <ellipse cx="0" cy="6" rx="4" ry="3"/>
      <rect x="3" y="-10" width="1.5" height="18"/>
    </g>
  </g>
  
  <!-- Sound waves -->
  <g stroke="#20ddbb" stroke-width="2" fill="none" opacity="0.3">
    <path d="M 100 200 Q 120 180 140 200 Q 160 220 180 200"/>
    <path d="M 220 200 Q 240 180 260 200 Q 280 220 300 200"/>
    <path d="M 110 220 Q 130 200 150 220 Q 170 240 190 220"/>
    <path d="M 210 220 Q 230 200 250 220 Q 270 240 290 220"/>
  </g>
  
  <!-- Vinyl record accent -->
  <g transform="translate(320, 280)" opacity="0.4">
    <circle cx="0" cy="0" r="25" fill="none" stroke="#5d59ff" stroke-width="2"/>
    <circle cx="0" cy="0" r="15" fill="none" stroke="#5d59ff" stroke-width="1"/>
    <circle cx="0" cy="0" r="8" fill="none" stroke="#5d59ff" stroke-width="1"/>
    <circle cx="0" cy="0" r="3" fill="#5d59ff"/>
  </g>
  
  <!-- Microphone accent -->
  <g transform="translate(80, 280)" opacity="0.4">
    <rect x="-3" y="-15" width="6" height="20" rx="3" fill="#ff6b6b"/>
    <rect x="-5" y="5" width="10" height="3" fill="#ff6b6b"/>
    <rect x="-1" y="8" width="2" height="10" fill="#ff6b6b"/>
  </g>
</svg>
