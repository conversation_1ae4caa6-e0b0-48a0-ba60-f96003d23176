<svg xmlns="http://www.w3.org/2000/svg" width="400" height="400" viewBox="0 0 400 400">
  <!-- Definitions for gradients and animations -->
  <defs>
    <!-- Основной градиент фона -->
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#1E2136">
        <animate attributeName="stop-color" values="#1E2136; #141625; #212746; #1E2136" dur="12s" repeatCount="indefinite" />
      </stop>
      <stop offset="100%" stop-color="#141625">
        <animate attributeName="stop-color" values="#141625; #212746; #1E2136; #141625" dur="12s" repeatCount="indefinite" />
      </stop>
    </linearGradient>
    
    <!-- Градиент для виниловой пластинки -->
    <linearGradient id="vinylGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#202335" />
      <stop offset="40%" stop-color="#141625" />
      <stop offset="100%" stop-color="#0e101a" />
    </linearGradient>
    
    <!-- Градиент для пульсирующего круга -->
    <radialGradient id="pulseGradient" cx="50%" cy="50%" r="50%" fx="50%" fy="50%">
      <stop offset="0%" stop-color="#20DDBB" stop-opacity="0.8">
        <animate attributeName="stop-opacity" values="0.8;0.4;0.8" dur="4s" repeatCount="indefinite" />
      </stop>
      <stop offset="70%" stop-color="#5D59FF" stop-opacity="0.4">
        <animate attributeName="stop-opacity" values="0.4;0.1;0.4" dur="4s" repeatCount="indefinite" />
      </stop>
      <stop offset="100%" stop-color="#5D59FF" stop-opacity="0" />
    </radialGradient>
    
    <!-- Эффекты свечения -->
    <filter id="glow" x="-20%" y="-20%" width="140%" height="140%">
      <feGaussianBlur stdDeviation="5" result="blur" />
      <feComposite in="SourceGraphic" in2="blur" operator="over" />
    </filter>
    
    <!-- Клипмаск для круглой аватарки -->
    <clipPath id="circleMask">
      <circle cx="200" cy="200" r="140" />
    </clipPath>
    
    <!-- Градиент для свечения от элементов -->
    <linearGradient id="glowGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#20DDBB" />
      <stop offset="100%" stop-color="#5D59FF" />
    </linearGradient>
  </defs>
  
  <!-- Фон с анимированным градиентом -->
  <rect width="400" height="400" fill="url(#bgGradient)" rx="30" ry="30" />
  
  <!-- Фоновая сетка -->
  <g opacity="0.1">
    <path d="M0,40 L400,40 M0,80 L400,80 M0,120 L400,120 M0,160 L400,160 M0,200 L400,200 M0,240 L400,240 M0,280 L400,280 M0,320 L400,320 M0,360 L400,360" 
          stroke="#5D59FF" stroke-width="0.5" />
    <path d="M40,0 L40,400 M80,0 L80,400 M120,0 L120,400 M160,0 L160,400 M200,0 L200,400 M240,0 L240,400 M280,0 L280,400 M320,0 L320,400 M360,0 L360,400" 
          stroke="#5D59FF" stroke-width="0.5" />
  </g>
  
  <!-- Пульсирующий круг -->
  <circle cx="200" cy="200" r="150" fill="url(#pulseGradient)" opacity="0.3">
    <animate attributeName="r" values="150;155;150" dur="4s" repeatCount="indefinite" />
    <animate attributeName="opacity" values="0.3;0.5;0.3" dur="4s" repeatCount="indefinite" />
  </circle>
  
  <!-- Декоративные круги и линии -->
  <g opacity="0.6">
    <circle cx="200" cy="200" r="165" stroke="#20DDBB" stroke-width="0.5" fill="none" stroke-dasharray="1,5">
      <animateTransform attributeName="transform" type="rotate" from="0 200 200" to="360 200 200" dur="60s" repeatCount="indefinite" />
    </circle>
    <circle cx="200" cy="200" r="155" stroke="#5D59FF" stroke-width="0.5" fill="none" stroke-dasharray="1,3">
      <animateTransform attributeName="transform" type="rotate" from="360 200 200" to="0 200 200" dur="40s" repeatCount="indefinite" />
    </circle>
  </g>
  
  <!-- Виниловая пластинка как основа аватара -->
  <g clip-path="url(#circleMask)">
    <!-- Основной круг пластинки -->
    <circle cx="200" cy="200" r="140" fill="url(#vinylGradient)" />
    
    <!-- Концентрические круги на пластинке -->
    <g opacity="0.6">
      <circle cx="200" cy="200" r="135" fill="none" stroke="#20DDBB" stroke-width="0.3" />
      <circle cx="200" cy="200" r="125" fill="none" stroke="#5D59FF" stroke-width="0.3" />
      <circle cx="200" cy="200" r="115" fill="none" stroke="#20DDBB" stroke-width="0.3" />
      <circle cx="200" cy="200" r="105" fill="none" stroke="#5D59FF" stroke-width="0.3" />
      <circle cx="200" cy="200" r="95" fill="none" stroke="#20DDBB" stroke-width="0.3" />
      <circle cx="200" cy="200" r="85" fill="none" stroke="#5D59FF" stroke-width="0.3" />
      <circle cx="200" cy="200" r="75" fill="none" stroke="#20DDBB" stroke-width="0.3" />
    </g>
    
    <!-- Центральное отверстие пластинки -->
    <circle cx="200" cy="200" r="20" fill="#0e101a" stroke="#20DDBB" stroke-width="1" />
    <circle cx="200" cy="200" r="6" fill="#5D59FF" />
  </g>
  
  <!-- Музыкальные элементы вокруг аватара -->
  <g filter="url(#glow)">
    <!-- Музыкальная нота 1 -->
    <g transform="translate(100, 70)" fill="url(#glowGradient)" opacity="0.8">
      <circle cx="0" cy="0" r="6" />
      <path d="M0,0 L0,40 C0,50 20,50 20,40 L20,30" fill="none" stroke="url(#glowGradient)" stroke-width="3" stroke-linecap="round" />
      <circle cx="20" cy="30" r="8" />
      <animate attributeName="opacity" values="0.8;0.4;0.8" dur="3s" repeatCount="indefinite" />
      <animateTransform attributeName="transform" type="translate" values="0,0; 5,-5; 0,0" dur="5s" repeatCount="indefinite" />
    </g>
    
    <!-- Музыкальная нота 2 -->
    <g transform="translate(280, 100)" fill="url(#glowGradient)" opacity="0.6">
      <circle cx="0" cy="0" r="5" />
      <path d="M0,0 L0,30 C0,40 15,40 15,30 L15,20" fill="none" stroke="url(#glowGradient)" stroke-width="2.5" stroke-linecap="round" />
      <circle cx="15" cy="20" r="7" />
      <animate attributeName="opacity" values="0.6;0.3;0.6" dur="4s" repeatCount="indefinite" />
      <animateTransform attributeName="transform" type="translate" values="0,0; -5,-5; 0,0" dur="6s" repeatCount="indefinite" />
    </g>
    
    <!-- Музыкальная нота 3 -->
    <g transform="translate(300, 280)" fill="url(#glowGradient)" opacity="0.7">
      <circle cx="0" cy="0" r="5.5" />
      <path d="M0,0 L0,35 C0,45 17,45 17,35 L17,25" fill="none" stroke="url(#glowGradient)" stroke-width="2.8" stroke-linecap="round" />
      <circle cx="17" cy="25" r="7.5" />
      <animate attributeName="opacity" values="0.7;0.4;0.7" dur="5s" repeatCount="indefinite" />
      <animateTransform attributeName="transform" type="translate" values="0,0; 5,5; 0,0" dur="7s" repeatCount="indefinite" />
    </g>
    
    <!-- Музыкальная нота 4 -->
    <g transform="translate(80, 300)" fill="url(#glowGradient)" opacity="0.65">
      <circle cx="0" cy="0" r="6" />
      <path d="M0,0 L0,35 C0,45 18,45 18,35 L18,25" fill="none" stroke="url(#glowGradient)" stroke-width="3" stroke-linecap="round" />
      <circle cx="18" cy="25" r="8" />
      <animate attributeName="opacity" values="0.65;0.35;0.65" dur="4.5s" repeatCount="indefinite" />
      <animateTransform attributeName="transform" type="translate" values="0,0; -5,5; 0,0" dur="5.5s" repeatCount="indefinite" />
    </g>
  </g>
  
  <!-- Звуковые волны -->
  <g transform="translate(200, 200)" opacity="0.5">
    <g fill="none" stroke="#20DDBB" stroke-linecap="round">
      <!-- Вертикальные звуковые волны -->
      <g>
        <line x1="0" y1="-170" x2="0" y2="-150" stroke-width="2">
          <animate attributeName="y2" values="-150; -160; -150" dur="1.2s" repeatCount="indefinite" />
        </line>
        <line x1="10" y1="-170" x2="10" y2="-145" stroke-width="2">
          <animate attributeName="y2" values="-145; -165; -145" dur="0.9s" repeatCount="indefinite" />
        </line>
        <line x1="20" y1="-170" x2="20" y2="-155" stroke-width="2">
          <animate attributeName="y2" values="-155; -165; -155" dur="1.1s" repeatCount="indefinite" />
        </line>
        <line x1="30" y1="-170" x2="30" y2="-150" stroke-width="2">
          <animate attributeName="y2" values="-150; -160; -150" dur="1s" repeatCount="indefinite" />
        </line>
        <line x1="40" y1="-170" x2="40" y2="-140" stroke-width="2">
          <animate attributeName="y2" values="-140; -155; -140" dur="1.3s" repeatCount="indefinite" />
        </line>
        
        <line x1="-10" y1="-170" x2="-10" y2="-145" stroke-width="2">
          <animate attributeName="y2" values="-145; -165; -145" dur="1.1s" repeatCount="indefinite" />
        </line>
        <line x1="-20" y1="-170" x2="-20" y2="-155" stroke-width="2">
          <animate attributeName="y2" values="-155; -165; -155" dur="0.8s" repeatCount="indefinite" />
        </line>
        <line x1="-30" y1="-170" x2="-30" y2="-150" stroke-width="2">
          <animate attributeName="y2" values="-150; -160; -150" dur="1.2s" repeatCount="indefinite" />
        </line>
        <line x1="-40" y1="-170" x2="-40" y2="-140" stroke-width="2">
          <animate attributeName="y2" values="-140; -155; -140" dur="1s" repeatCount="indefinite" />
        </line>
      </g>
    </g>
  </g>
  
  <!-- Вращающаяся установка для DJ -->
  <g transform="translate(200, 340)">
    <!-- Основание DJ-пульта -->
    <rect x="-45" y="-15" width="90" height="30" rx="5" ry="5" fill="#20DDBB" opacity="0.2" />
    
    <!-- Кнопки на пульте -->
    <circle cx="-25" cy="0" r="5" fill="#20DDBB" opacity="0.7" />
    <circle cx="0" cy="0" r="5" fill="#5D59FF" opacity="0.7" />
    <circle cx="25" cy="0" r="5" fill="#20DDBB" opacity="0.7" />
    
    <!-- Анимированный ползунок -->
    <rect x="-40" y="10" width="80" height="3" rx="1.5" ry="1.5" fill="#5D59FF" opacity="0.5" />
    <rect x="-25" y="8" width="10" height="7" rx="2" ry="2" fill="#20DDBB" opacity="0.8">
      <animate attributeName="x" values="-25; 15; -25" dur="3s" repeatCount="indefinite" />
    </rect>
  </g>
  
  <!-- Имитация текста Music Artist -->
  <g transform="translate(200, 85)">
    <path d="M-70,0 L-40,0 M-30,0 L-20,0 M-10,0 L10,0 M20,0 L40,0 M50,0 L70,0" stroke="#5D59FF" stroke-width="2" opacity="0.7" />
    <path d="M-50,-15 L-40,-15 M-30,-15 L-10,-15 M0,-15 L20,-15 M30,-15 L50,-15" stroke="#20DDBB" stroke-width="2" opacity="0.7" />
  </g>
</svg> 