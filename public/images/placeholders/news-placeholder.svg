<svg width="600" height="400" viewBox="0 0 600 400" fill="none" xmlns="http://www.w3.org/2000/svg">
    <!-- Dark background -->
    <rect width="600" height="400" fill="#272B43"/>
    
    <!-- Gradient overlay -->
    <rect width="600" height="400" fill="url(#paint0_linear)"/>
    
    <!-- Purple accent lines -->
    <path d="M0 80H600" stroke="#8B5CF6" stroke-width="1" stroke-opacity="0.2"/>
    <path d="M0 160H600" stroke="#8B5CF6" stroke-width="1" stroke-opacity="0.2"/>
    <path d="M0 240H600" stroke="#8B5CF6" stroke-width="1" stroke-opacity="0.2"/>
    <path d="M0 320H600" stroke="#8B5CF6" stroke-width="1" stroke-opacity="0.2"/>
    
    <!-- Vertical lines -->
    <path d="M120 0V400" stroke="#8B5CF6" stroke-width="1" stroke-opacity="0.2"/>
    <path d="M240 0V400" stroke="#8B5CF6" stroke-width="1" stroke-opacity="0.2"/>
    <path d="M360 0V400" stroke="#8B5CF6" stroke-width="1" stroke-opacity="0.2"/>
    <path d="M480 0V400" stroke="#8B5CF6" stroke-width="1" stroke-opacity="0.2"/>
    
    <!-- News icon -->
    <g transform="translate(250, 130)">
        <rect x="10" y="10" width="80" height="100" rx="4" fill="#1E2136" stroke="#8B5CF6" stroke-width="2"/>
        <rect x="25" y="30" width="50" height="4" rx="2" fill="#8B5CF6"/>
        <rect x="25" y="40" width="50" height="4" rx="2" fill="#8B5CF6" fill-opacity="0.6"/>
        <rect x="25" y="50" width="30" height="4" rx="2" fill="#8B5CF6" fill-opacity="0.6"/>
        <rect x="25" y="70" width="50" height="4" rx="2" fill="#8B5CF6" fill-opacity="0.4"/>
        <rect x="25" y="80" width="50" height="4" rx="2" fill="#8B5CF6" fill-opacity="0.4"/>
        <rect x="25" y="90" width="30" height="4" rx="2" fill="#8B5CF6" fill-opacity="0.4"/>
    </g>
    
    <!-- Text labels -->
    <text x="300" y="270" font-family="Arial" font-size="18" font-weight="bold" text-anchor="middle" fill="#FFFFFF">News Image</text>
    <text x="300" y="295" font-family="Arial" font-size="14" text-anchor="middle" fill="#FFFFFF" opacity="0.6">Coming soon</text>
    
    <!-- Abstract decorative elements -->
    <circle cx="100" cy="100" r="40" fill="#8B5CF6" fill-opacity="0.05"/>
    <circle cx="500" cy="300" r="60" fill="#8B5CF6" fill-opacity="0.05"/>
    
    <!-- Definitions -->
    <defs>
        <linearGradient id="paint0_linear" x1="0" y1="0" x2="600" y2="400" gradientUnits="userSpaceOnUse">
            <stop stop-color="#1E2136" stop-opacity="0.8"/>
            <stop offset="1" stop-color="#1E2136" stop-opacity="0.3"/>
        </linearGradient>
    </defs>
</svg> 