import { database, Query } from "@/libs/AppWriteClient"


const useGetProfileById = async (profileId: string) => {
    try {
        const response = await database.listDocuments(
            String(process.env.NEXT_PUBLIC_DATABASE_ID), 
            String(process.env.NEXT_PUBLIC_COLLECTION_ID_PROFILE), 
            [ 
                Query.equal('$id', profileId) 
            ]
        );
        const documents = response.documents;
        return {
            id: documents[0]?.$id,
            user_id: documents[0]?.user_id,
            name: documents[0]?.name,
            image: documents[0]?.image,
            bio: documents[0]?.bio
        }
    } catch (error) {
        throw error
    }
}

export default useGetProfileById