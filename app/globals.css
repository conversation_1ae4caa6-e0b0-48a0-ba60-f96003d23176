@import './styles/animations.css';
@import './styles/auth-mobile.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

/* 3D Fire Icon Animation Styles */
.fire-icon-3d {
  position: relative;
  transform-style: preserve-3d;
  animation: flame-flicker 3s ease-in-out infinite;
}

.fire-gradient {
  fill: url(#fire-gradient);
  filter: drop-shadow(0 0 8px rgba(255, 94, 0, 0.5));
}

@keyframes flame-flicker {
  0%, 100% {
    transform: rotateY(0deg) scale(1);
    filter: drop-shadow(0 0 8px rgba(255, 140, 0, 0.7));
  }
  25% {
    transform: rotateY(10deg) scale(1.05);
    filter: drop-shadow(0 0 12px rgba(255, 94, 0, 0.8));
  }
  50% {
    transform: rotateY(-10deg) scale(1.1);
    filter: drop-shadow(0 0 15px rgba(255, 60, 0, 0.9));
  }
  75% {
    transform: rotateY(5deg) scale(1.05);
    filter: drop-shadow(0 0 12px rgba(255, 94, 0, 0.8));
  }
}

/* SVG Gradient definition for fire icons */
@layer components {
  .text-transparent {
    color: transparent;
    background: linear-gradient(45deg, #ff8a00, #ff5e00, #ff3d00, #ff5e00);
    background-size: 200% 200%;
    background-clip: text;
    -webkit-background-clip: text;
    animation: gradient-shift 3s ease infinite;
  }
}

@keyframes gradient-shift {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

/* Подключение фшрифта Gotham Pro */
@font-face {
    font-family: 'Gotham Pro';
    src: url('/fonts/GothamPro.woff') format('woff');
    font-weight: 300;
    font-style: normal;
    font-display: swap;
  }
  
  


  
  /* Применение шрифта Gotham Pro */
  body {
    font-family: 'Gotham Pro', sans-serif;
  }
  

  section {
    display: flex;
    overflow-x: hidden;
  }
  
  div-run {
    font-size: 70px;
    font-family: sans-serif;
    line-height: 1.3;
    padding: 24px 0;
    white-space: nowrap;
    display: inline-block;
  }
  
  .div-run1 {
    animation: marquee1 200s infinite linear;
    animation-delay: -200s;
  }
  
  .div-run1 {
    animation: marquee2 200s infinite linear;
    animation-delay: -10s;
  }
  
  @keyframes marquee1 {
    0% {
      transform: translateX(100%);
    }
    100% {
      transform: translateX(-100%);
    }
  }
  
  @keyframes marquee2 {
    0% {
      transform: translateX(0%);
    }
    100% {
      transform: translateX(-200%);
    }
  }
  

  /*BG*/
  .gradient-bg {
    position: fixed; /* Сделаем позиционирование фиксированным */
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: -1; /* Установите и на уровень ниже содержимого */
}

.gradients-container {
    position: absolute;
    width: 100%;
    height: 100%;
}

.interactive {
  position: absolute;
  width: 600px;
  height: 600px;
  border-radius: 50%;
  opacity: 0.2;
  filter: blur(50px);
  top: 0;
  left: 0;
  background-color: rgba(32, 221, 187, 0.4); /* Бирюзовый с 40% непрозрачностью */
  mix-blend-mode: screen;
}

.dynamic-bubble {
  position: absolute;
  width: 500px;
  height: 500px;
  border-radius: 50%;
  filter: blur(50px);
  opacity: 0.3;
}

.dynamic-bubble.pink {
  background-color: rgba(255, 105, 180, 0.4); /* Розовый с 40% непрозрачностью */
}

.dynamic-bubble.purple {
  background-color: rgba(46, 36, 105, 0.4); /* Темно-синий с 40% непрозрачностью */
}

.icon-hover {
  @apply transition-transform duration-200 hover:scale-110;
}

/* Стили для иконок в TopNav */
#TopNav img, 
#TopNav svg {
  @apply transition-transform duration-200;
}

#TopNav img:hover, 
#TopNav svg:hover {
  @apply scale-110;
}

/* Стили для модального окна с высшим приоритетом */
.modal-overlay {
  position: fixed;
  inset: 0;
  z-index: 100;
  background-color: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(4px);
}

/* Специальные стили для WelcomeModal с максимальным z-index */
.welcome-modal-wrapper {
  position: relative;
  z-index: 1000000 !important;
}

.welcome-modal-wrapper .modal-overlay {
  z-index: 999990 !important;
  background-color: rgba(0, 0, 0, 0.7) !important;
  backdrop-filter: blur(15px) !important;
}

.welcome-modal-wrapper .modal-content {
  z-index: 1000001 !important;
  position: relative;
}

/* Когда WelcomeModal активен, блокируем прокрутку body */
body.welcome-modal-open {
  overflow: hidden !important;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: scale(0.95) translateY(-10px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

@keyframes progress {
    0% {
        width: 0%;
    }
    100% {
        width: 100%;
    }
}

.animate-fadeIn {
    animation: fadeIn 0.3s ease-out forwards;
}

.animate-progress {
    animation: progress 2.5s ease-in-out forwards;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(-10px);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.animate-float {
    animation: float 3s ease-in-out infinite;
}

.animate-fadeInUp {
    animation: fadeInUp 0.6s ease-out forwards;
}

.animation-delay-200 {
    animation-delay: 200ms;
}

@keyframes border-glow {
  0% {
    border-color: rgba(255, 255, 255, 0.1);
    box-shadow: 0 0 20px rgba(32, 221, 187, 0.1);
  }
  50% {
    border-color: rgba(32, 221, 187, 0.3);
    box-shadow: 0 0 25px rgba(32, 221, 187, 0.2);
  }
  100% {
    border-color: rgba(255, 255, 255, 0.1);
    box-shadow: 0 0 20px rgba(32, 221, 187, 0.1);
  }
}

.animate-border-glow {
  animation: border-glow 3s ease-in-out infinite;
}

/* Добавляем анимацию свечения для элементов */
@keyframes glow {
  0% {
    filter: drop-shadow(0 0 3px rgba(32, 221, 187, 0.3));
  }
  50% {
    filter: drop-shadow(0 0 10px rgba(32, 221, 187, 0.7));
  }
  100% {
    filter: drop-shadow(0 0 3px rgba(32, 221, 187, 0.3));
  }
}

.animate-glow {
  animation: glow 3s ease-in-out infinite;
}

/* Анимация для hover-эффекта подъема элементов */
.hover-lift {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 20px -5px rgba(32, 221, 187, 0.2);
}

/* Анимация блеска градиентов */
@keyframes shimmer {
  0% {
    background-position: -100% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.animate-shimmer {
  animation: shimmer 3s linear infinite;
  background-size: 200% 100%;
}

/* Анимация плавного движения вверх-вниз */
@keyframes floatY {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-5px);
  }
  100% {
    transform: translateY(0px);
  }
}

.animate-floatY {
  animation: floatY 4s ease-in-out infinite;
}

/* Анимация появления с масштабированием */
@keyframes scaleIn {
  0% {
    transform: scale(0.95);
    opacity: 0;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.animate-scaleIn {
  animation: scaleIn 0.6s ease-out forwards;
}

/* Анимация постепенного появления снизу */
@keyframes fadeInUp {
  0% {
    transform: translateY(20px);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

.animate-fadeInUp {
  animation: fadeInUp 0.5s ease-out forwards;
}

/* Задержки для анимаций */
.animation-delay-200 {
  animation-delay: 0.2s;
}

.animation-delay-400 {
  animation-delay: 0.4s;
}

.animation-delay-600 {
  animation-delay: 0.6s;
}

/* Градиентный текст */
.gradient-text {
  @apply bg-gradient-to-r from-[#20DDBB] to-[#8B5CF6] bg-clip-text text-transparent;
}

/* Стеклянный эффект для карточек */
.glass-effect {
  @apply bg-white/5 backdrop-blur-sm border border-white/10;
}

@keyframes gradient {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.animate-gradient {
  background-size: 200% 200%;
  animation: gradient 6s ease infinite;
}

/* Custom Scrollbar */
.custom-scrollbar::-webkit-scrollbar {
  width: 5px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #20DDBB;
  border-radius: 20px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #1CB399;
}

/* Gradient Border */
.gradient-border {
  position: relative;
  background: linear-gradient(to right, #20DDBB, #018CFD);
  padding: 1px;
  border-radius: 0.75rem;
}

.gradient-border.always-glow::before {
  content: '';
  position: absolute;
  inset: -2px;
  background: linear-gradient(to right, #20DDBB, #018CFD);
  border-radius: 0.85rem;
  filter: blur(8px);
  opacity: 0.15;
  z-index: -1;
}

/* Terms Page Specific Styles */
.terms-section {
  scroll-margin-top: 100px;
}

.terms-section-title {
  @apply text-2xl font-bold text-white mb-6;
  background: linear-gradient(to right, #20DDBB, #018CFD);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

@layer utilities {
  /* Existing utility classes */
  
  /* Add shimmer animation for status badges */
  @keyframes shimmer {
    0% {
      transform: translateX(-100%);
    }
    100% {
      transform: translateX(100%);
    }
  }
  
  .animate-shimmer {
    animation: shimmer 2s infinite;
  }
  
  /* Enhanced shimmer animation for status badges */
  .status-badge-shimmer {
    position: relative;
    overflow: hidden;
  }
  
  .status-badge-shimmer::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background: linear-gradient(90deg, 
      rgba(255, 255, 255, 0) 0%, 
      rgba(255, 255, 255, 0.1) 50%, 
      rgba(255, 255, 255, 0) 100%);
    background-size: 200% 100%;
    animation: shimmer 2.5s infinite linear;
  }
  
  /* Status badge glow effects */
  .status-badge-approved {
    box-shadow: 0 0 15px rgba(16, 185, 129, 0.3);
  }
  
  .status-badge-pending {
    box-shadow: 0 0 15px rgba(245, 158, 11, 0.3);
  }
  
  .status-badge-rejected {
    box-shadow: 0 0 15px rgba(239, 68, 68, 0.3);
  }
  
  /* Pulse animation for pending status */
  @keyframes pulse-glow {
    0% {
      box-shadow: 0 0 5px rgba(245, 158, 11, 0.3);
    }
    50% {
      box-shadow: 0 0 15px rgba(245, 158, 11, 0.5);
    }
    100% {
      box-shadow: 0 0 5px rgba(245, 158, 11, 0.3);
    }
  }
  
  .animate-pulse-glow {
    animation: pulse-glow 2s ease-in-out infinite;
  }
}

/* Плавная прокрутка для контента */
html {
  scroll-behavior: smooth;
}

body {
  overflow-x: hidden;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}

/* Обеспечивает плавное взаимодействие контента с нижними панелями */
.smooth-scroll-container {
  transition: transform 0.3s ease;
}

/* Стилизация для z-индексов */
.fixed-bottom-panel {
  z-index: 50;
  box-shadow: 0 -8px 30px rgba(0, 0, 0, 0.25);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
}

/* Адаптивный отступ для контента */
@media (max-width: 768px) {
  .content-with-bottom-nav {
    padding-bottom: 65px;
  }
}

/* Top navigation spacing - ensures content doesn't overlap with nav */
.content-spacing {
  padding-top: 80px; /* Provides adequate spacing from the 60px high nav bar */
  margin-top: 0;
}

/* Adjusts the main content containers to use this spacing consistently */
.content-with-top-nav {
  padding-top: 80px !important;
  margin-top: 0 !important;
  position: relative;
  z-index: 1;
}

.pt-\[80px\] {
  padding-top: 80px !important;
}

/* Replace pt-[100px] with content-with-top-nav */
.pt-\[100px\] {
  padding-top: 80px !important;
}

/* Ensure main layout elements respect the top nav */
.smooth-scroll-container {
  padding-top: 60px;
}

/* For post detail pages */
.post-detail-container {
  padding-top: 80px !important;
}

/* Fix background elements to position properly under the top nav */
#TopNav {
  position: fixed;
  z-index: 50;
  top: 0;
  left: 0;
  right: 0;
  height: 60px;
}