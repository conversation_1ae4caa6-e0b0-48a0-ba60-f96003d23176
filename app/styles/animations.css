@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

@keyframes slideIn {
    from {
        transform: translateX(-20px);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.animate-fadeIn {
    animation: fadeIn 0.3s ease-out forwards;
}

.animate-pulse-custom {
    animation: pulse 0.3s ease-in-out;
}

.animate-slideIn {
    animation: slideIn 0.3s ease-out forwards;
}

/* Hover animations */
.hover-scale {
    transition: transform 0.2s ease-out;
}

.hover-scale:hover {
    transform: scale(1.02);
}

/* Loading animation */
@keyframes shimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

.loading-shimmer {
    background: linear-gradient(
        90deg,
        rgba(46, 36, 105, 0.1) 25%,
        rgba(46, 36, 105, 0.2) 37%,
        rgba(46, 36, 105, 0.1) 63%
    );
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
}

@keyframes glow {
    0%, 100% {
        filter: drop-shadow(0 0 5px rgba(32, 221, 187, 0.3));
    }
    50% {
        filter: drop-shadow(0 0 15px rgba(32, 221, 187, 0.5));
    }
}

@keyframes floatY {
    0%, 100% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(-5px);
    }
}

@keyframes scaleIn {
    from {
        transform: scale(0.95);
        opacity: 0;
    }
    to {
        transform: scale(1);
        opacity: 1;
    }
}

@keyframes gradientFlow {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}

.animate-glow {
    animation: glow 2s ease-in-out infinite;
}

.animate-floatY {
    animation: floatY 3s ease-in-out infinite;
}

.animate-scaleIn {
    animation: scaleIn 0.3s ease-out forwards;
}

.gradient-text {
    background: linear-gradient(45deg, #20DDBB, #2E2469, #FF69B4);
    background-size: 200% 200%;
    animation: gradientFlow 3s ease infinite;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hover-lift {
    transition: transform 0.2s ease-out, box-shadow 0.2s ease-out;
}

.hover-lift:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}

/* Glass effect */
.glass-effect {
    background: rgba(46, 36, 105, 0.2);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Ripple effect */
@keyframes ripple {
    0% {
        box-shadow: 0 0 0 0 rgba(32, 221, 187, 0.2);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(32, 221, 187, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(32, 221, 187, 0);
    }
}

.animate-ripple {
    animation: ripple 1.5s infinite;
}

@keyframes floatComment {
    0% {
        opacity: 0;
        transform: translateX(-50px) translateY(20px);
    }
    10%, 90% {
        opacity: 1;
        transform: translateX(0) translateY(0);
    }
    100% {
        opacity: 0;
        transform: translateX(50px) translateY(-20px);
    }
}

.animate-floatComment {
    animation: floatComment var(--animation-duration, 8s) ease-in-out forwards;
}

/* Enhanced glass effect for floating comments */
.glass-effect-comment {
    background: rgba(46, 36, 105, 0.15);
    backdrop-filter: blur(8px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 
        0 4px 6px -1px rgba(0, 0, 0, 0.1),
        0 2px 4px -1px rgba(0, 0, 0, 0.06),
        inset 0 0 20px rgba(32, 221, 187, 0.05);
}

@keyframes shine {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}

.animate-shine {
    animation: shine 2s infinite;
} 

/* Glassmorphism for TopNavGuide */
.topnav-glass {
  background: rgba(36, 24, 61, 0.85);
  backdrop-filter: blur(18px) saturate(1.2);
  border: 1.5px solid rgba(255,255,255,0.10);
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.25);
}

/* Floating animation for tooltips */
@keyframes floatUpDown {
  0% { transform: translateY(0px); }
  50% { transform: translateY(-8px); }
  100% { transform: translateY(0px); }
}

.topnav-float {
  animation: floatUpDown 2.8s ease-in-out infinite;
} 