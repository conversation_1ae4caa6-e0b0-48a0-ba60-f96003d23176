/* Navigation animations optimized for performance */

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes menuEnter {
  from { 
    opacity: 0; 
    transform: scale(0.95) translateY(10px);
  }
  to { 
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.animate-fadeIn {
  animation: fadeIn 0.2s ease-out forwards;
  will-change: opacity;
}

.animate-menuEnter {
  animation: menuEnter 0.2s ease-out forwards;
  will-change: opacity, transform;
}

/* Apply contain style to heavy animations for performance */
.contain-paint {
  contain: paint;
}

/* Optimize animations using transform and opacity */
.hover-scale {
  transition: transform 0.2s ease;
  will-change: transform;
}
.hover-scale:hover {
  transform: scale(1.05);
}

/* Optimize shimmer effect */
.shimmer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.3) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  transform: translateX(-100%);
  will-change: transform;
}

.group:hover .shimmer {
  animation: shimmer 1s ease-out;
}

@keyframes shimmer {
  to {
    transform: translateX(100%);
  }
} 