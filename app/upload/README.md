# Процесс загрузки и обработки треков

Этот документ содержит подробное описание процесса загрузки и обработки аудио треков в приложении.

## Общий обзор процесса

Процесс загрузки и публикации трека включает следующие этапы:

1. **Выбор файлов пользователем**
   - Загрузка WAV-файла аудио трека
   - Загрузка изображения для обложки

2. **Обработка аудио на стороне клиента**
   - Конвертация WAV в MP3 с использованием FFmpeg WebAssembly
   - Сегментация MP3 на фрагменты для потокового воспроизведения (HLS)
   - Создание M3U8 плейлиста

3. **Загрузка файлов в облачное хранилище Appwrite**
   - Оптимизация изображения обложки
   - Загрузка основного аудио файла
   - Загрузка MP3 файла
   - Загрузка сегментов и M3U8 плейлиста

4. **Создание записи в базе данных**
   - Сохранение метаданных трека (название, жанр и т.д.)
   - Сохранение ссылок на загруженные файлы

## Подробное описание компонентов

### 1. Клиентская обработка аудио (FFmpeg WebAssembly)

Обработка аудио файлов происходит полностью на стороне клиента с использованием WebAssembly версии FFmpeg:

- **Преимущества клиентской обработки:**
  - Снижение нагрузки на сервер
  - Уменьшение объема передаваемых данных
  - Работоспособность даже при ограниченных серверных ресурсах

- **Требования для корректной работы:**
  - Современный браузер с поддержкой WebAssembly
  - Поддержка SharedArrayBuffer (требуются заголовки безопасности)
  - Достаточно оперативной памяти на устройстве пользователя

- **Компоненты обработки:**
  - `useClientAudioProcessor.ts` - хук для конвертации WAV в MP3 и создания сегментов
  - `ClientAudioProcessor.tsx` - компонент для отображения состояния обработки

### 2. Процесс сегментации для HLS-стриминга

Для обеспечения потокового воспроизведения MP3 файл разбивается на сегменты:

- Каждый сегмент имеет длительность 10 секунд
- Сегменты создаются с помощью FFmpeg на стороне клиента
- Для каждого трека генерируется M3U8 плейлист, который содержит ссылки на сегменты
- После загрузки сегментов плейсхолдеры в M3U8 заменяются на реальные ID файлов

### 3. Загрузка файлов в Appwrite

Все файлы загружаются в хранилище Appwrite с использованием клиентского SDK:

- **Типы загружаемых файлов:**
  - Оригинальный WAV файл
  - Конвертированный MP3 файл
  - Сегменты MP3 для потокового воспроизведения
  - M3U8 плейлист
  - Оптимизированное изображение обложки

- **Процесс загрузки:**
  - Файлы загружаются последовательно с отображением прогресса
  - Для каждого файла создается уникальный ID
  - Загруженные файлы доступны через API Appwrite с использованием их ID

### 4. Создание записи в базе данных

После успешной загрузки всех файлов создается запись в базе данных:

- **Хранимые данные:**
  - `user_id` - ID пользователя, загрузившего трек
  - `audio_url` - ID оригинального аудио файла
  - `image_url` - ID изображения обложки
  - `mp3_url` - ID конвертированного MP3 файла
  - `m3u8_url` - ID M3U8 плейлиста
  - `trackname` - название трека
  - `genre` - жанр трека
  - `segments` - JSON-строка с ID всех сегментов
  - `created_at` - дата создания записи
  - `plays` - количество прослушиваний (начальное значение '0')

## Требования к браузеру и устройству

Для корректной работы клиентской обработки аудио необходимо:

1. **Браузер:**
   - Chrome, Firefox, Edge последних версий
   - Включенная поддержка JavaScript и WebAssembly
   - Не рекомендуется использовать режим инкогнито

2. **Заголовки безопасности для SharedArrayBuffer:**
   - Сайт должен обслуживаться через HTTPS
   - Необходимы заголовки Cross-Origin-Opener-Policy и Cross-Origin-Embedder-Policy

3. **Устройство:**
   - Минимум 4 ГБ оперативной памяти
   - Достаточно свободного места на диске для временных файлов

## Возможные проблемы и их решения

### 1. Ошибка "SharedArrayBuffer is not defined"

**Причина:** Отсутствуют необходимые заголовки безопасности или используется устаревший браузер.

**Решение:**
- Убедитесь, что сайт обслуживается через HTTPS
- Проверьте наличие правильных заголовков безопасности
- Используйте современный браузер (не в режиме инкогнито)

### 2. Ошибка при загрузке файлов

**Причина:** Превышение лимитов размера файлов или проблемы с подключением.

**Решение:**
- Проверьте размер загружаемых файлов (рекомендуется <50MB для WAV)
- Убедитесь в стабильности интернет-соединения
- Проверьте доступность сервисов Appwrite

### 3. Проблемы с записью в базу данных

**Причина:** Несоответствие типов данных или отсутствие обязательных полей.

**Решение:**
- Проверьте правильность передаваемых данных
- Убедитесь, что все обязательные поля заполнены
- Проверьте соответствие типов данных схеме коллекции

## Техническая архитектура

```
app/
├── upload/                    # Директория загрузки
│   ├── page.tsx               # Основной компонент страницы загрузки
│   └── README.md              # Этот документ
├── components/upload/
│   ├── ClientAudioProcessor.tsx  # Компонент обработки аудио
│   └── ...
├── hooks/
│   ├── useClientAudioProcessor.ts  # Хук для обработки аудио
│   ├── useClientCreatePost.ts      # Хук для создания поста
│   ├── useClientUpload.ts          # Хук для загрузки файлов
│   └── ...
└── ...
```

## Поток данных

1. Пользователь выбирает WAV-файл → Файл загружается в память браузера
2. FFmpeg WebAssembly конвертирует WAV в MP3 → Создается MP3-файл в памяти браузера
3. FFmpeg создает сегменты и M3U8 плейлист → Файлы хранятся в памяти браузера
4. Все файлы загружаются в Appwrite → Получаем ID файлов
5. Создается запись в базе данных → Получаем ID трека
6. Пользователь перенаправляется на страницу успешной загрузки

## Схема компонентов и их взаимодействия

```
Пользователь
   │
   ▼
Страница загрузки (Upload)
   │
   ├─► Выбор файлов
   │
   ├─► ClientAudioProcessor 
   │      │
   │      └─► useClientAudioProcessor ─► FFmpeg WebAssembly
   │
   ├─► useClientCreatePost
   │      │
   │      ├─► Оптимизация изображения
   │      │
   │      └─► useClientUpload ─► Appwrite Storage
   │
   └─► Создание записи в БД ─► Appwrite Database
```

## Деплой на Vercel

Для успешного развертывания приложения на платформе Vercel необходимо учесть следующие особенности:

### Настройка заголовков безопасности

В файле `middleware.ts` и `next.config.js` должны быть настроены заголовки безопасности для поддержки SharedArrayBuffer:

```js
// middleware.ts
response.headers.set('Cross-Origin-Opener-Policy', 'same-origin');
response.headers.set('Cross-Origin-Embedder-Policy', 'require-corp');
```

### Настройка Vercel для работы с WebAssembly

1. **Настройка сборки**
   - Убедитесь, что в `vercel.json` (если используется) или в настройках проекта на платформе Vercel отключена минификация, которая может повлиять на модули WASM.

2. **Переменные окружения**
   - Все переменные окружения из `.env` файла должны быть добавлены в настройки проекта на Vercel.

3. **Лимиты функций**
   - Стандартные лимиты Serverless Functions могут быть недостаточны для обработки аудио на сервере. Наше клиентское решение обходит эти ограничения.

### Проверка после деплоя

После деплоя рекомендуется проверить следующее:

1. Открыть консоль разработчика в браузере и убедиться, что нет ошибок, связанных с загрузкой WASM модулей.
2. Проверить заголовки ответа сервера, они должны содержать COOP и COEP заголовки.
3. Загрузить тестовый трек для проверки полного цикла работы приложения. 