/* Base styles */
.container {
  @apply max-w-7xl mx-auto;
}

.pageContent {
  @apply space-y-0;
}

/* Grid container with improved scrolling */
.gridContainer {
  @apply pt-5;
  height: calc(100vh - 90px);
  overflow-y: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
  -webkit-overflow-scrolling: touch;
}

.gridContainer::-webkit-scrollbar {
  display: none;
}

/* Hide scrollbar for any element with this class */
.hideScrollbar {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

.hideScrollbar::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

.gridLayout {
  display: grid;
  grid-template-columns: repeat(1, minmax(0, 1fr));
  gap: 1rem;
  padding: 0 10px 0 10px; /* No bottom padding */
}

@media (min-width: 640px) {
  .gridLayout {
    grid-template-columns: repeat(2, minmax(0, 1fr));
    padding: 0 1rem 0 1rem; /* No bottom padding */
  }
}

@media (min-width: 1024px) {
  .gridLayout {
    grid-template-columns: repeat(3, minmax(0, 1fr));
    gap: 1.5rem;
    padding: 0 1.5rem 0 1.5rem; /* No bottom padding */
  }
}

.userCard {
  @apply relative overflow-hidden rounded-2xl aspect-[4/5] cursor-pointer;
  background: linear-gradient(to bottom right, rgba(37, 40, 64, 0.8), rgba(30, 33, 54, 0.8));
}

.cardBackground {
  @apply absolute inset-0 bg-gradient-to-b from-transparent via-black/40 to-black;
}

.cardGlass {
  @apply absolute inset-0 bg-black/30;
}

.searchContainer {
  @apply sticky top-0 z-30 backdrop-blur-md;
  height: 60px;
  margin-bottom: 10px;
  background: linear-gradient(to bottom,
    rgba(26, 26, 46, 0.95) 0%,
    rgba(26, 26, 46, 0.8) 70%,
    rgba(26, 26, 46, 0.4) 90%,
    transparent 100%
  );
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.searchWrapper {
  @apply max-w-7xl mx-auto px-4 h-full flex items-center justify-between gap-4;
}

.searchInputWrapper {
  @apply flex-1 lg:flex-none relative;
  max-width: 450px;
  height: 48px;
}

.searchInput {
  @apply w-full h-full backdrop-blur-md text-white border rounded-2xl pl-12 pr-4 text-sm font-medium;
  @apply focus:outline-none focus:ring-2 focus:border-transparent transition-all duration-300 ease-in-out;
  background-color: rgba(255, 255, 255, 0.08);
  border-color: rgba(32, 221, 187, 0.3);
  box-shadow: 0 4px 20px rgba(32, 221, 187, 0.1);
}

.searchInput::placeholder {
  color: rgba(255, 255, 255, 0.6);
}

.searchInput:focus {
  background-color: rgba(255, 255, 255, 0.1);
  border-color: rgba(32, 221, 187, 0.5);
  --tw-ring-color: rgba(32, 221, 187, 0.6);
}

.searchInput:focus {
  box-shadow: 0 8px 32px rgba(32, 221, 187, 0.2);
  transform: translateY(-1px);
}

.searchIcon {
  @apply absolute left-4 top-1/2 transform -translate-y-1/2 text-[#20DDBB] transition-all duration-300;
}

.searchInput:focus + .searchIcon {
  @apply text-[#20DDBB] scale-110;
}

.filterGroup {
  @apply flex items-center gap-3;
  height: 48px;
}

.filterButton {
  @apply flex items-center justify-center rounded-2xl border transition-all duration-300;
  @apply hover:shadow-lg;
  background-color: rgba(255, 255, 255, 0.08);
  border-color: rgba(255, 255, 255, 0.2);
  height: 48px;
  min-width: 48px;
}

.filterButton:hover {
  background-color: rgba(255, 255, 255, 0.12);
  border-color: rgba(32, 221, 187, 0.4);
  box-shadow: 0 10px 25px rgba(32, 221, 187, 0.1);
}

.filterButton:hover {
  transform: translateY(-1px);
}

.filterDropdown {
  @apply backdrop-blur-md border rounded-2xl px-4 py-2 text-white text-sm font-medium;
  @apply transition-all duration-300 cursor-pointer focus:outline-none focus:ring-2;
  background-color: rgba(255, 255, 255, 0.08);
  border-color: rgba(255, 255, 255, 0.2);
  height: 48px;
  min-width: 120px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.filterDropdown:hover {
  background-color: rgba(255, 255, 255, 0.12);
  border-color: rgba(32, 221, 187, 0.4);
}

.filterDropdown:focus {
  --tw-ring-color: rgba(32, 221, 187, 0.6);
}

.filterDropdown:hover {
  transform: translateY(-1px);
  box-shadow: 0 8px 32px rgba(32, 221, 187, 0.15);
}

.loadingPulse {
  @apply animate-pulse;
}

.loadingCard {
  @apply bg-white/5 rounded-2xl aspect-[4/5];
}

/* Enhanced mobile navigation - No jerking, smooth animations */
.mobileNav {
  @apply fixed bottom-0 left-0 right-0 z-50 lg:hidden flex items-center justify-center;
  background: linear-gradient(to top,
    rgba(26, 26, 46, 1) 0%,
    rgba(26, 26, 46, 0.98) 70%,
    rgba(26, 26, 46, 0.95) 100%
  );
  backdrop-filter: blur(24px);
  -webkit-backdrop-filter: blur(24px);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow:
    0 -10px 30px rgba(0, 0, 0, 0.4),
    0 -1px 0 rgba(32, 221, 187, 0.15),
    0 -20px 40px rgba(32, 221, 187, 0.08);
  transform: translateZ(0); /* Force hardware acceleration */
  will-change: transform;
  height: 60px; /* Увеличена для лучшего UX */
  padding: 0 1.5rem;
  /* Prevent layout shifts */
  position: fixed;
  contain: layout style paint;
  /* Smooth transitions */
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Mobile-first responsive container sizes */
@media (max-width: 480px) {
  .gridContainer {
    max-width: 100%;
    padding: 20px 0 0 0;
    height: calc(100vh - 150px); /* Adjusted for enhanced mobile nav height */
    /* Improve scroll performance */
    -webkit-overflow-scrolling: touch;
    transform: translateZ(0);
    will-change: scroll-position;
    /* Prevent bounce scrolling */
    overscroll-behavior: contain;
  }



  .pageContent {
    padding: 0;
    margin: 0;
    /* Ensure smooth transitions */
    transition: all 0.3s ease;
  }

  .gridLayout {
    padding: 0 10px 0 10px; /* No bottom padding - cards go to edge */
    gap: 0.75rem;
  }

  /* Prevent layout shifts on mobile */
  .searchContainer {
    position: sticky;
    top: 0;
    z-index: 30;
    transform: translateZ(0);
    will-change: transform;
    /* Enhanced backdrop */
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
  }

  /* Enhanced mobile button styles */
  .mobileNavButton {
    position: relative;
    padding: 0.75rem 1.5rem;
    border-radius: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    transform: translateZ(0);
    will-change: transform;
    -webkit-tap-highlight-color: transparent;
  }

  .mobileNavButton:active {
    transform: scale(0.95);
    background: rgba(32, 221, 187, 0.15);
    border-color: rgba(32, 221, 187, 0.3);
  }

  .mobileNavButton:hover {
    background: rgba(255, 255, 255, 0.08);
    border-color: rgba(32, 221, 187, 0.2);
    box-shadow: 0 4px 20px rgba(32, 221, 187, 0.1);
  }
}

@media (min-width: 481px) and (max-width: 768px) {
  .gridContainer {
    max-width: 100%;
    padding: 20px 1rem 0 1rem; /* Remove bottom padding */
  }

  .pageContent {
    padding: 0;
    margin: 0;
  }
}

@media (min-width: 769px) {
  .gridContainer {
    max-width: 100%;
    padding: 20px 0 0 0;
    height: calc(100vh - 90px);
  }
}

@media (max-width: 480px) {
  .userCard {
    min-height: 320px;
    max-height: 420px;
  }
  
  .userCard button {
    transform: scale(0.9);
  }
}

@media (max-width: 480px) {
  .grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 1rem;
    padding: 1rem;
  }
}

@media (min-width: 481px) and (max-width: 768px) {
  .grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 1.5rem;
    padding: 1.5rem;
  }
}

@media (max-width: 480px) {
  .fadeIn {
    animation: fadeIn 0.3s ease-in-out;
  }
  
  .userCard {
    transform: none;
  }
  
  .userCard:active {
    transform: scale(0.98);
  }
}

/* Убираем скроллбары на всех устройствах */
.custom-scrollbar {
  scrollbar-width: none;
  -ms-overflow-style: none;
  -webkit-overflow-scrolling: touch;
}

.custom-scrollbar::-webkit-scrollbar {
  display: none;
}

@media (max-width: 480px) {
  .loadingCard {
    min-height: 320px;
  }
  
  .animate-shimmer {
    animation: shimmer 2s infinite linear;
  }
}

@media (max-width: 480px) {
  .filterDropdown {
    position: relative;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .filterDropdown button {
    width: 100%;
    padding: 0.75rem;
    font-size: 0.875rem;
    text-align: center;
  }
  
  .filterDropdown .options {
    width: 100%;
    max-height: 60vh;
    overflow-y: auto;
  }
}

/* Дублирующиеся стили удалены - используется основной .mobileNav выше */

@media (max-width: 480px) {
  .touchFeedback {
    transition: transform 0.2s ease;
  }
  
  .touchFeedback:active {
    transform: scale(0.95);
  }
}

@media (max-width: 480px) {
  .modal {
    position: fixed;
    inset: 0;
    z-index: 100;
  }
  
  .modalContent {
    width: 100%;
    max-height: 90vh;
    overflow-y: auto;
  }
}

.pullToRefresh {
  height: 60px;
  overflow: hidden;
}

@media (max-width: 480px) {
  .pullToRefresh {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(26, 26, 46, 0.95);
    backdrop-filter: blur(10px);
    transform-origin: top;
    transition: transform 0.2s ease;
  }
}

.searchBar {
  width: 100%;
  padding: 0.75rem 1rem;
  border-radius: 0.5rem;
}

.searchBar:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(32, 221, 187, 0.2);
  border-color: rgba(32, 221, 187, 0.5);
}

.filterContainer {
  display: flex;
  gap: 0.5rem;
  padding: 0.5rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 0.5rem;
  backdrop-filter: blur(10px);
}

.icon {
  width: 1.5rem;
  height: 1.5rem;
}

.iconSmall {
  width: 1.25rem;
  height: 1.25rem;
}

.button {
  padding: 0.5rem 1rem;
}

.buttonPrimary {
  background: rgba(32, 221, 187, 0.2);
}

.buttonSecondary {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fadeIn {
  animation: fadeIn 0.3s ease-in-out;
}

/* Убираем дублирующиеся стили - уже определены выше */

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.animate-shimmer {
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0.03) 25%,
    rgba(255, 255, 255, 0.08) 37%,
    rgba(255, 255, 255, 0.03) 63%
  );
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

/* Убираем дублирующиеся стили - уже определены выше */

/* Mobile top ranked modal - Enhanced */
@media (max-width: 480px) {
  .topRankedModal {
    @apply fixed inset-0 z-50;
    background: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
  }

  .topRankedContent {
    @apply absolute bottom-0 left-0 right-0 rounded-t-3xl max-h-[85vh] overflow-hidden;
    background: linear-gradient(to top, #1A1A2E 0%, #252840 100%);
    transform: translateY(0);
    transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow:
      0 -20px 40px rgba(0, 0, 0, 0.4),
      0 -1px 0 rgba(32, 221, 187, 0.1);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
  }

  .topRankedHeader {
    @apply sticky top-0 z-10 px-5 pb-4 border-b border-white/5;
    background: linear-gradient(to top, #1A1A2E 0%, #252840 100%);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
  }

  .topRankedList {
    @apply px-5 pb-6 overflow-y-auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
    -webkit-overflow-scrolling: touch;
  }

  .topRankedList::-webkit-scrollbar {
    display: none;
  }

  .topRankedItem {
    @apply mb-3 last:mb-0 p-4 rounded-2xl border transition-all duration-300;
    background: linear-gradient(to right, rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.03));
    border-color: rgba(255, 255, 255, 0.05);
    transform: translateZ(0);
    will-change: transform;
  }

  .topRankedItem:active {
    transform: scale(0.98);
    background: linear-gradient(to right, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
  }

  .topRankedItem.topThree {
    border-color: rgba(32, 221, 187, 0.2);
    background: linear-gradient(to right, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
  }
}

/* Enhanced mobile search bar positioning and styling */
@media (max-width: 768px) {
  .mobileSearchContainer {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    left: clamp(2rem, 10%, 3rem);
    right: clamp(1rem, 5%, 2rem);
    z-index: 50;
    min-width: 300px;
    max-width: calc(100vw - 4rem);
    width: clamp(300px, 50vw, 600px);
  }

  .mobileSearchInput {
    width: 100%;
    padding: 1rem 3rem 1rem 1.25rem; /* More padding for better UX */
    background: rgba(26, 26, 46, 0.98);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 1.5rem;
    color: white;
    font-size: 1rem; /* Larger font for better readability */
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow:
      0 8px 32px rgba(0, 0, 0, 0.3),
      0 0 0 1px rgba(32, 221, 187, 0.1);
  }

  .mobileSearchInput:focus {
    outline: none;
    border-color: rgba(32, 221, 187, 0.6);
    background: rgba(26, 26, 46, 1);
    box-shadow:
      0 0 0 2px rgba(32, 221, 187, 0.3),
      0 12px 40px rgba(0, 0, 0, 0.4),
      0 0 30px rgba(32, 221, 187, 0.2);
    transform: translateY(-2px) scale(1.02);
  }

  .mobileSearchInput::placeholder {
    color: rgba(156, 163, 175, 0.8);
    font-weight: 400;
  }

  /* Enhanced search button styles */
  .searchButton {
    padding: 0.75rem;
    background: rgba(32, 221, 187, 0.1);
    border: 1px solid rgba(32, 221, 187, 0.3);
    border-radius: 1rem;
    transition: all 0.3s ease;
  }

  .searchButton:hover {
    background: rgba(32, 221, 187, 0.2);
    border-color: rgba(32, 221, 187, 0.5);
    transform: scale(1.05);
  }

  .searchButton:active {
    transform: scale(0.95);
  }
}

/* Extra small screens - adjust search field */
@media (max-width: 400px) {
  .mobileSearchContainer {
    left: 1rem;
    right: 0.5rem;
    min-width: 280px;
    width: calc(100vw - 2rem);
  }

  .mobileSearchInput {
    padding: 0.875rem 2.5rem 0.875rem 1rem;
    font-size: 0.875rem;
  }
}

/* Smooth animations for mobile interactions */
@media (max-width: 480px) {
  .touchOptimized {
    -webkit-tap-highlight-color: transparent;
    touch-action: manipulation;
  }

  .smoothTransition {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    transform: translateZ(0);
    will-change: transform;
  }

  .activeScale:active {
    transform: scale(0.98);
  }

  .hoverGlow:hover {
    box-shadow: 0 0 20px rgba(32, 221, 187, 0.3);
  }
}